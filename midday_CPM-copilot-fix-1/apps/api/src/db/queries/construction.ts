import { type SQL, desc, eq, and, or, like, ilike, sql } from "drizzle-orm";
import { primaryDb as db } from "../index";
import {
  constructionProjects,
  constructionProgressUpdates,
  constructionFiles,
  constructionAnnotations,
  siteMeasurements,
  users,
  customers,
} from "../schema";

export async function getConstructionProjects({
  teamId,
  userId,
  status,
  search,
  limit = 50,
  offset = 0,
}: {
  teamId: string;
  userId?: string;
  status?: string;
  search?: string;
  limit?: number;
  offset?: number;
}) {
  const conditions = [eq(constructionProjects.teamId, teamId)];

  if (status && status !== "all") {
    conditions.push(eq(constructionProjects.status, status as any));
  }

  if (search) {
    conditions.push(
      or(
        ilike(constructionProjects.name, `%${search}%`),
        ilike(constructionProjects.description, `%${search}%`),
        ilike(constructionProjects.location, `%${search}%`)
      )
    );
  }

  return await db
    .select({
      id: constructionProjects.id,
      name: constructionProjects.name,
      description: constructionProjects.description,
      status: constructionProjects.status,
      completionPercentage: constructionProjects.completionPercentage,
      estimatedCost: constructionProjects.estimatedCost,
      actualCost: constructionProjects.actualCost,
      currentPhase: constructionProjects.currentPhase,
      location: constructionProjects.location,
      address: constructionProjects.address,
      siteArea: constructionProjects.siteArea,
      buildingArea: constructionProjects.buildingArea,
      currency: constructionProjects.currency,
      startDate: constructionProjects.startDate,
      endDate: constructionProjects.endDate,
      siteCoordinates: constructionProjects.siteCoordinates,
      contractorInfo: constructionProjects.contractorInfo,
      permitInfo: constructionProjects.permitInfo,
      projectFiles: constructionProjects.projectFiles,
      teamId: constructionProjects.teamId,
      createdBy: constructionProjects.createdBy,
      createdAt: constructionProjects.createdAt,
      updatedAt: constructionProjects.updatedAt,
      customer: {
        id: customers.id,
        name: customers.name,
        email: customers.email,
      },
    })
    .from(constructionProjects)
    .leftJoin(customers, eq(constructionProjects.customerId, customers.id))
    .where(and(...conditions))
    .orderBy(desc(constructionProjects.updatedAt))
    .limit(limit)
    .offset(offset);
}

export async function getConstructionProject({
  teamId,
  projectId,
}: {
  teamId: string;
  projectId: string;
}) {
  const result = await db
    .select({
      id: constructionProjects.id,
      name: constructionProjects.name,
      description: constructionProjects.description,
      status: constructionProjects.status,
      completionPercentage: constructionProjects.completionPercentage,
      estimatedCost: constructionProjects.estimatedCost,
      actualCost: constructionProjects.actualCost,
      currentPhase: constructionProjects.currentPhase,
      location: constructionProjects.location,
      address: constructionProjects.address,
      siteArea: constructionProjects.siteArea,
      buildingArea: constructionProjects.buildingArea,
      currency: constructionProjects.currency,
      startDate: constructionProjects.startDate,
      endDate: constructionProjects.endDate,
      siteCoordinates: constructionProjects.siteCoordinates,
      contractorInfo: constructionProjects.contractorInfo,
      permitInfo: constructionProjects.permitInfo,
      projectFiles: constructionProjects.projectFiles,
      teamId: constructionProjects.teamId,
      createdBy: constructionProjects.createdBy,
      createdAt: constructionProjects.createdAt,
      updatedAt: constructionProjects.updatedAt,
      customer: {
        id: customers.id,
        name: customers.name,
        email: customers.email,
        phone: customers.phone,
      },
      createdByUser: {
        id: users.id,
        fullName: users.fullName,
        email: users.email,
      },
    })
    .from(constructionProjects)
    .leftJoin(customers, eq(constructionProjects.customerId, customers.id))
    .leftJoin(users, eq(constructionProjects.createdBy, users.id))
    .where(
      and(
        eq(constructionProjects.teamId, teamId),
        eq(constructionProjects.id, projectId)
      )
    )
    .then((rows) => rows[0]);

  return result;
}

export async function createConstructionProject({
  teamId,
  userId,
  name,
  description,
  customerId,
  estimatedCost,
  location,
  siteArea,
  buildingArea,
  currency,
  startDate,
  endDate,
  latitude,
  longitude,
  metadata,
}: {
  teamId: string;
  userId: string;
  name: string;
  description?: string;
  customerId?: string;
  estimatedCost?: number;
  location?: string;
  siteArea?: number;
  buildingArea?: number;
  currency?: string;
  startDate?: Date;
  endDate?: Date;
  latitude?: number;
  longitude?: number;
  metadata?: Record<string, any>;
}) {
  // Build siteCoordinates object if coordinates are provided
  const siteCoordinates = (latitude !== undefined && longitude !== undefined) ? {
    latitude,
    longitude,
    ...metadata,
  } : null;

  const result = await db
    .insert(constructionProjects)
    .values({
      teamId,
      createdBy: userId,
      name,
      description: description || null,
      customerId: customerId || null,
      estimatedCost: estimatedCost || null,
      location: location || null,
      siteArea: siteArea || null,
      buildingArea: buildingArea || null,
      currency: currency || "USD",
      startDate: startDate ? startDate.toISOString() : null,
      endDate: endDate ? endDate.toISOString() : null,
      siteCoordinates,
      status: "planning",
      completionPercentage: 0,
      actualCost: null,
    })
    .returning();

  return result[0];
}

export async function updateConstructionProject({
  teamId,
  projectId,
  data,
}: {
  teamId: string;
  projectId: string;
  data: Partial<typeof constructionProjects.$inferInsert>;
}) {
  const result = await db
    .update(constructionProjects)
    .set({ 
      ...data, 
      updatedAt: new Date().toISOString(),
    })
    .where(
      and(
        eq(constructionProjects.teamId, teamId),
        eq(constructionProjects.id, projectId)
      )
    )
    .returning();

  return result[0];
}

export async function deleteConstructionProject({
  teamId,
  projectId,
}: {
  teamId: string;
  projectId: string;
}) {
  return await db
    .delete(constructionProjects)
    .where(
      and(
        eq(constructionProjects.teamId, teamId),
        eq(constructionProjects.id, projectId)
      )
    );
}

export async function getConstructionProgressUpdates({
  teamId,
  projectId,
  limit = 50,
  offset = 0,
}: {
  teamId: string;
  projectId: string;
  limit?: number;
  offset?: number;
}) {
  return await db
    .select({
      id: constructionProgressUpdates.id,
      title: constructionProgressUpdates.title,
      description: constructionProgressUpdates.description,
      phase: constructionProgressUpdates.phase,
      completionPercentage: constructionProgressUpdates.completionPercentage,
      attachmentUrls: constructionProgressUpdates.attachmentUrls,
      metadata: constructionProgressUpdates.metadata,
      createdAt: constructionProgressUpdates.createdAt,
      user: {
        id: users.id,
        fullName: users.fullName,
        email: users.email,
      },
    })
    .from(constructionProgressUpdates)
    .leftJoin(users, eq(constructionProgressUpdates.userId, users.id))
    .where(
      and(
        eq(constructionProgressUpdates.teamId, teamId),
        eq(constructionProgressUpdates.projectId, projectId)
      )
    )
    .orderBy(desc(constructionProgressUpdates.createdAt))
    .limit(limit)
    .offset(offset);
}

export async function createProgressUpdate({
  teamId,
  projectId,
  userId,
  title,
  description,
  phase,
  completionPercentage,
  attachmentUrls,
  metadata,
}: {
  teamId: string;
  projectId: string;
  userId: string;
  title: string;
  description?: string;
  phase?: string;
  completionPercentage?: number;
  attachmentUrls?: string[];
  metadata?: Record<string, any>;
}) {
  const result = await db
    .insert(constructionProgressUpdates)
    .values({
      teamId,
      projectId,
      userId,
      title,
      description,
      phase: phase as any,
      completionPercentage,
      attachmentUrls,
      metadata,
    })
    .returning();

  // Update project completion percentage if provided
  if (completionPercentage !== undefined) {
    await db
      .update(constructionProjects)
      .set({
        completionPercentage,
        currentPhase: phase as any,
        updatedAt: new Date().toISOString(),
      })
      .where(
        and(
          eq(constructionProjects.teamId, teamId),
          eq(constructionProjects.id, projectId)
        )
      );
  }

  return result[0];
}

export async function getConstructionFiles({
  teamId,
  projectId,
  fileType,
  limit = 50,
  offset = 0,
}: {
  teamId: string;
  projectId: string;
  fileType?: string;
  limit?: number;
  offset?: number;
}) {
  const conditions = [
    eq(constructionFiles.teamId, teamId),
    eq(constructionFiles.projectId, projectId),
  ];

  if (fileType) {
    conditions.push(eq(constructionFiles.fileType, fileType as any));
  }

  return await db
    .select({
      id: constructionFiles.id,
      fileName: constructionFiles.fileName,
      fileType: constructionFiles.fileType,
      fileSize: constructionFiles.fileSize,
      fileUrl: constructionFiles.fileUrl,
      mimeType: constructionFiles.mimeType,
      version: constructionFiles.version,
      isLatest: constructionFiles.isLatest,
      description: constructionFiles.description,
      tags: constructionFiles.tags,
      relatedPhase: constructionFiles.relatedPhase,
      createdAt: constructionFiles.createdAt,
      uploadedBy: {
        id: users.id,
        fullName: users.fullName,
        email: users.email,
      },
    })
    .from(constructionFiles)
    .leftJoin(users, eq(constructionFiles.uploadedBy, users.id))
    .where(and(...conditions))
    .orderBy(desc(constructionFiles.createdAt))
    .limit(limit)
    .offset(offset);
}

export async function uploadConstructionFile({
  teamId,
  projectId,
  userId,
  fileName,
  fileType,
  fileSize,
  fileUrl,
  mimeType,
  description,
  tags,
  relatedPhase,
  parentFileId,
}: {
  teamId: string;
  projectId: string;
  userId: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  fileUrl: string;
  mimeType: string;
  description?: string;
  tags?: string[];
  relatedPhase?: string;
  parentFileId?: string;
}) {
  const result = await db
    .insert(constructionFiles)
    .values({
      teamId,
      projectId,
      uploadedBy: userId,
      fileName,
      fileType: fileType as any,
      fileSize,
      fileUrl,
      mimeType,
      description,
      tags,
      relatedPhase: relatedPhase as any,
      parentFileId,
      version: 1,
      isLatest: true,
    })
    .returning();

  return result[0];
}

export async function getConstructionAnnotations({
  teamId,
  projectId,
  status,
  limit = 50,
  offset = 0,
}: {
  teamId: string;
  projectId: string;
  status?: string;
  limit?: number;
  offset?: number;
}) {
  const conditions = [
    eq(constructionAnnotations.teamId, teamId),
    eq(constructionAnnotations.projectId, projectId),
  ];

  if (status) {
    conditions.push(eq(constructionAnnotations.status, status as any));
  }

  return await db
    .select({
      id: constructionAnnotations.id,
      content: constructionAnnotations.content,
      annotationType: constructionAnnotations.annotationType,
      status: constructionAnnotations.status,
      priority: constructionAnnotations.priority,
      position: constructionAnnotations.position,
      relatedPhase: constructionAnnotations.relatedPhase,
      dueDate: constructionAnnotations.dueDate,
      resolvedAt: constructionAnnotations.resolvedAt,
      createdAt: constructionAnnotations.createdAt,
      user: {
        id: users.id,
        fullName: users.fullName,
        email: users.email,
      },
      assignedTo: {
        id: users.id,
        fullName: users.fullName,
        email: users.email,
      },
    })
    .from(constructionAnnotations)
    .leftJoin(users, eq(constructionAnnotations.userId, users.id))
    .leftJoin(
      users,
      eq(constructionAnnotations.assignedTo, users.id)
    )
    .where(and(...conditions))
    .orderBy(desc(constructionAnnotations.createdAt))
    .limit(limit)
    .offset(offset);
}

export async function createAnnotation({
  teamId,
  projectId,
  userId,
  content,
  annotationType,
  priority,
  position,
  relatedPhase,
  dueDate,
  assignedTo,
  relatedFileId,
}: {
  teamId: string;
  projectId: string;
  userId: string;
  content: string;
  annotationType: string;
  priority?: string;
  position?: Record<string, any>;
  relatedPhase?: string;
  dueDate?: Date;
  assignedTo?: string;
  relatedFileId?: string;
}) {
  const result = await db
    .insert(constructionAnnotations)
    .values({
      teamId,
      projectId,
      userId,
      content,
      annotationType: annotationType as any,
      priority: priority as any,
      position,
      relatedPhase: relatedPhase as any,
      dueDate,
      assignedTo,
      relatedFileId,
      status: "open",
    })
    .returning();

  return result[0];
}

export async function getSiteMeasurements({
  teamId,
  projectId,
}: {
  teamId: string;
  projectId: string;
}) {
  return await db
    .select()
    .from(siteMeasurements)
    .where(
      and(
        eq(siteMeasurements.teamId, teamId),
        eq(siteMeasurements.projectId, projectId)
      )
    )
    .orderBy(desc(siteMeasurements.createdAt));
}

export async function createSiteMeasurement({
  teamId,
  projectId,
  userId,
  measurementType,
  coordinates,
  measurements,
  units,
  notes,
  metadata,
}: {
  teamId: string;
  projectId: string;
  userId: string;
  measurementType: string;
  coordinates: Record<string, any>;
  measurements: Record<string, any>;
  units: string;
  notes?: string;
  metadata?: Record<string, any>;
}) {
  const result = await db
    .insert(siteMeasurements)
    .values({
      teamId,
      projectId,
      userId,
      measurementType: measurementType as any,
      coordinates,
      measurements,
      units,
      notes,
      metadata,
    })
    .returning();

  return result[0];
}