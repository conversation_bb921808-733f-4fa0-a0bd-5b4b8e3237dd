import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../init";
import {
  getConstructionProjects,
  getConstructionProject,
  createConstructionProject,
  updateConstructionProject,
  deleteConstructionProject,
} from "../../db/queries/construction";

const createConstructionProjectSchema = z.object({
  name: z.string().min(1, "Project name is required"),
  description: z.string().optional(),
  customerId: z.string().optional(),
  estimatedCost: z.number().min(0).optional(),
  location: z.string().optional(),
  siteArea: z.number().min(0).optional(),
  buildingArea: z.number().min(0).optional(),
  currency: z.string().default("USD"),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
  metadata: z.record(z.any()).optional(),
});

const updateConstructionProjectSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  status: z.enum(["planning", "in_progress", "on_hold", "completed", "cancelled"]).optional(),
  completionPercentage: z.number().min(0).max(100).optional(),
  actualCost: z.number().min(0).optional(),
  currentPhase: z.enum([
    "planning",
    "site_preparation",
    "foundation",
    "framing",
    "roofing",
    "electrical",
    "plumbing",
    "hvac",
    "insulation",
    "drywall",
    "flooring",
    "painting",
    "fixtures",
    "final_inspection",
    "completion"
  ]).optional(),
  estimatedCost: z.number().min(0).optional(),
  location: z.string().optional(),
  siteArea: z.number().min(0).optional(),
  buildingArea: z.number().min(0).optional(),
  currency: z.string().optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
  metadata: z.record(z.any()).optional(),
});

export const constructionProjectsRouter = createTRPCRouter({
  list: protectedProcedure
    .input(
      z.object({
        status: z.string().optional(),
        search: z.string().optional(),
        limit: z.number().min(1).max(100).default(50),
        offset: z.number().min(0).default(0),
      })
    )
    .query(async ({ ctx, input }) => {
      return getConstructionProjects({
        teamId: ctx.user.teamId,
        userId: ctx.user.id,
        status: input.status,
        search: input.search,
        limit: input.limit,
        offset: input.offset,
      });
    }),

  get: protectedProcedure
    .input(z.object({ projectId: z.string() }))
    .query(async ({ ctx, input }) => {
      return getConstructionProject({
        teamId: ctx.user.teamId,
        projectId: input.projectId,
      });
    }),

  create: protectedProcedure
    .input(createConstructionProjectSchema)
    .mutation(async ({ ctx, input }) => {
      return createConstructionProject({
        teamId: ctx.user.teamId,
        userId: ctx.user.id,
        ...input,
      });
    }),

  update: protectedProcedure
    .input(
      z.object({
        projectId: z.string(),
        data: updateConstructionProjectSchema,
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Convert Date objects to ISO strings for database storage
      const processedData = {
        ...input.data,
        startDate: input.data.startDate?.toISOString(),
        endDate: input.data.endDate?.toISOString(),
      };
      
      return updateConstructionProject({
        teamId: ctx.user.teamId,
        projectId: input.projectId,
        data: processedData,
      });
    }),

  delete: protectedProcedure
    .input(z.object({ projectId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return deleteConstructionProject({
        teamId: ctx.user.teamId,
        projectId: input.projectId,
      });
    }),
});